@tailwind base;
@tailwind components;
@tailwind utilities;

/* Hexadash Template Custom Styles */

/* Font Face Declarations */
@font-face {
  font-family: 'Jost';
  src: url('/assets/vendor_assets/fonts/Jost-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: 'Jost';
  src: url('/assets/vendor_assets/fonts/Jost-Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'Jost';
  src: url('/assets/vendor_assets/fonts/Jost-SemiBold.ttf') format('truetype');
  font-weight: 600;
  font-style: normal;
}

@font-face {
  font-family: 'Jost';
  src: url('/assets/vendor_assets/fonts/Jost-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
}

/* Custom Scrollbar */
.scrollbar::-webkit-scrollbar {
  width: 6px;
}

.scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.scrollbar::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 10px;
}

.scrollbar::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Dark mode scrollbar */
.dark .scrollbar::-webkit-scrollbar-track {
  background: #2d2d2d;
}

.dark .scrollbar::-webkit-scrollbar-thumb {
  background: #555;
}

.dark .scrollbar::-webkit-scrollbar-thumb:hover {
  background: #777;
}

/* Sidebar Styles */
.asidebar {
  transition: all 0.2s ease-linear;
}

.asidebar.collapsed {
  width: 80px !important;
}

.asidebar.collapsed .logo-full {
  display: none;
}

.asidebar.collapsed .logo-fold {
  display: block !important;
}

.asidebar.collapsed .title {
  display: none;
}

.asidebar.collapsed .arrow-down {
  display: none;
}

.asidebar.collapsed .slug {
  display: none;
}

/* Navigation Styles */
.sub-item-wrapper.open .sub-item {
  display: block !important;
}

.sub-item-wrapper .arrow-down {
  transition: transform 0.3s ease;
}

.sub-item-wrapper.open .arrow-down {
  transform: rotate(180deg);
}

/* Custom Components */
.badge-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
}

.dot-primary {
  background-color: #8231D3;
}

/* Animation Classes */
.spin-lg {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Card Styles */
.card {
  @apply bg-white dark:bg-box-dark rounded-lg shadow-regular;
}

.card-header {
  @apply p-6 border-b border-gray-200 dark:border-box-dark-up;
}

.card-body {
  @apply p-6;
}

/* Button Styles */
.btn {
  @apply px-4 py-2 rounded-md font-medium transition-all duration-200 ease-in-out;
}

.btn-primary {
  @apply bg-primary text-white hover:bg-primary-hbr;
}

.btn-secondary {
  @apply bg-secondary text-white hover:bg-secondary-hbr;
}

.btn-success {
  @apply bg-success text-white hover:bg-success-hbr;
}

.btn-warning {
  @apply bg-warning text-white hover:bg-warning-hbr;
}

.btn-danger {
  @apply bg-danger text-white hover:bg-danger-hbr;
}

.btn-info {
  @apply bg-info text-white hover:bg-info-hbr;
}

/* Form Styles */
.form-control {
  @apply w-full px-3 py-2 border border-gray-300 dark:border-box-dark-up rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-box-dark text-body dark:text-title-dark;
}

.form-label {
  @apply block text-sm font-medium text-body dark:text-title-dark mb-2;
}

/* Table Styles */
.table {
  @apply w-full text-left;
}

.table th {
  @apply px-6 py-3 bg-gray-50 dark:bg-box-dark-up text-xs font-medium text-gray-500 dark:text-subtitle-dark uppercase tracking-wider;
}

.table td {
  @apply px-6 py-4 whitespace-nowrap text-sm text-body dark:text-subtitle-dark;
}

/* Utility Classes */
.text-truncate {
  @apply truncate;
}

.border-light {
  @apply border-gray-200 dark:border-box-dark-up;
}

.bg-light {
  @apply bg-gray-50 dark:bg-box-dark-up;
}

/* Mobile Responsive */
@media (max-width: 1199px) {
  .asidebar {
    transform: translateX(-100%);
  }

  .asidebar.show {
    transform: translateX(0);
  }
}